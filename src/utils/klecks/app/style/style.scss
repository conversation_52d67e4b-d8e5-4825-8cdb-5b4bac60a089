/* --- entrypoint for app ---*/
@use 'vars';
@use './dark';

:root {
  --active-highlight-color: #{vars.$activeHighlightColor};
  --body-bg-color: #{vars.$bodyBgColor};
  --kl-color: #{vars.$klColor};
  --ui-bg-color: #{vars.$uiBgColor};
}

@keyframes consoleIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes consoleInFast {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
}

@keyframes consoleOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

body {
  overflow: hidden;
  font-size: 16px;
  overscroll-behavior-x: none; // prevent back gesture in Chrome
  color-scheme: light;
}

body,
html {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  touch-action: none;

  text-size-adjust: none;
  -webkit-text-size-adjust: none;
  -moz-text-size-adjust: none;
  -ms-text-size-adjust: none;
}

.g-root {
  /* some browsers like to overwrite body */
  font-size: 16px;
  font-family: system-ui, sans-serif;
}

a:link,
a:not([href]) {
  color: var(--kl-color);
  text-decoration: underline;
  cursor: pointer;
}
a:visited {
  color: #536184;
  text-decoration: underline;
}

a:hover,
a:not([href]):hover {
  color: #000;
  text-decoration: underline;
}

.kl-select {
  background: #fff;
}

.grid-hr {
  border-top: 1px solid rgba(0, 0, 0, 0.15);
  margin-top: 10px;
  margin-left: 10px;
  margin-right: 10px;
  clear: both;
}

.kl-toolspace {
  background-color: var(--ui-bg-color);
}

.kl-toolspace--left {
  border-right: 1px solid rgb(135, 135, 135);
}

.kl-toolspace--right {
  border-left: 1px solid rgb(135, 135, 135);
}

.kl-toolspace-row {
  background: linear-gradient(to top, rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.6) 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px #cecece;
}

.nohighlight {
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
}

.toolspace-row-button {
  cursor: pointer;
  flex-grow: 1;
}

.toolspace-triangle-button {
  cursor: pointer;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
}
.toolspace-svg-triangle-button {
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
}

.toolspace-row-button-hover {
  background: rgba(255, 255, 255, 0.7);
}

.toolspace-row-button:active,
.toolspace-triangle-button:active {
  background-color: #ddd;
  box-shadow: inset 2px 2px 10px rgba(0, 0, 0, 0.2) !important;
}

.toolspace-svg-triangle-button-hover {
  fill: rgba(255, 255, 255, 0.7);
}

.toolspace-svg-triangle-button:active {
  fill: #ddd;
  filter: url(#innershadow);
}

.toolspace-row-button-activated {
  background: #fff;
  box-shadow: inset 0 2px 0 0 var(--active-highlight-color);
  pointer-events: none;
}

.toolspace-row-button-activated > div:first-child {
  opacity: 1 !important;
}

.toolspace-row-button-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.kl-tooldropdown-caret {
  bottom: 1px;
  background-size: 60%;
  transition: all 100ms ease-out;
}
.toolspace-row-button-activated.kl-tool-button--hover > .kl-tooldropdown-caret {
  bottom: -1px;
}

.kl-tooldropdown-caret:hover {
  bottom: -1px;
}

.kl-tool-row-border-right {
  border-right: 1px solid rgb(212, 212, 212);
}

.kl-color-picker {
}

.kl-color-picker__h,
.kl-color-picker__sv,
.kl-color-picker__preview {
  box-shadow: 0 0 0 1px rgb(188, 188, 188);
}

.kl-color-picker--active {
  box-shadow: 0 0 0 1px #fff;
  z-index: 1;
}

.kl-color-picker__secondary {
  box-shadow: 0 0 0 1px rgb(188, 188, 188);
}

.color-picker-preview-button {
  cursor: pointer;
  color: #000;
  opacity: 0.8;
  text-align: center;
  user-select: none;
  -webkit-user-select: none; // safari
}

.color-picker-preview-button-hover {
  background-color: #fff;
  opacity: 1;
  filter: none !important;
}

.color-picker-preview-button-active {
  background-color: #fff;
  opacity: 1;
  box-shadow: inset 0 2px 0 0 var(--active-highlight-color);
  filter: none !important;
}

.slider-wrapper {
  border-radius: 8px;
  box-shadow: 0.5px 1px 0 0 rgba(255, 255, 255, 0.8);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  background: linear-gradient(to bottom, #909090 1%, #b3b3b3 73%);
  color: #333;
  cursor: ew-resize;
}

.slider-wrapper--active {
  background: linear-gradient(to bottom, #7a7a7a 1%, #939393 73%);
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.2);
  color: #000;
}

.slider-wrapper--active .slider-inner {
  box-shadow: 2px 2px 10px rgb(145, 145, 145);
  background: #fff;
}

.slider-inner {
  background-color: #fff;
  background-image: linear-gradient(to top, rgb(232, 232, 232) 0%, rgba(255, 255, 255, 1) 70%);
  box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.3), 2px 2px 6px rgba(145, 145, 145, 0.49);
  border-radius: 6px 0 0 0;
}

.kl-point-slider__line {
  height: 2px;
  background: #aaa;
}

.kl-point-slider__point {
  position: absolute;
  top: 0;
  background-color: #eaeaea;
  cursor: ew-resize;
  transition: box-shadow 0.2s ease-in-out;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.8);

  &:active {
    box-shadow: 0 0 6px rgba(0, 0, 0, 1);
  }
}

button {
  padding: 5px;
  min-width: 30px;
  font-size: 15px;
  line-height: 17px; // height of icons
  color: #666;
  border: none;
  background-color: #f3f3f3;
  border-radius: 4px;

  background-image: linear-gradient(to top, rgba(111, 111, 111, 0.1) 0%, rgba(255, 255, 255, 0) 60%, rgba(255, 255, 255, 0.3) 100%);

  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.04), 0 1px rgba(0, 0, 0, 0.1), 0 2px 3px rgba(0, 0, 0, 0.05);
  user-select: none;
  -webkit-user-select: none;
}

button:hover {
  background-color: #fff;
  color: #777;
  cursor: pointer;
}

button:active {
  background-color: #ddd;
  box-shadow: inset 2px 2px 10px rgba(0, 0, 0, 0.2), 0 2px 2px #ddd;
}

button:disabled {
  padding: 5px;
  min-width: 30px;
  font-size: 15px;
  color: #999;
  border: none;
  background-color: #ddd;
  border-radius: 4px;

  background-image: linear-gradient(to top, rgba(255, 255, 255, 0) 60%, rgba(255, 255, 255, 0.3) 100%);
  cursor: default;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

button:disabled img {
  opacity: 0.2;
}

button img {
  float: left;
  pointer-events: none;
}

.kl-button-primary {
  background: var(--kl-color);
  color: #fff;
}
.kl-button-primary:hover {
  background-image: linear-gradient(to top, var(--kl-color) -50%, var(--active-highlight-color) 150%);
  color: #fff;
}
.kl-button-primary:active {
  background-image: linear-gradient(to top, var(--kl-color) -50%, var(--active-highlight-color) 120%);
  color: #fff;
  box-shadow: unset;
}

.grid-button {
  width: 120px;
  margin-left: 10px;
  margin-top: 10px;
}

.grid-button--filter {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.kl-popup {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  background: rgba(50, 50, 50, 0.5);
  animation-name: consoleIn;
  animation-duration: 0.3s;
  animation-timing-function: ease-out;
  touch-action: pan-y;
}

.kl-popup-box {
  position: relative;
  background-color: #ddd;
  border-radius: 10px;
  padding: 20px;
  margin: 10px 0;
  box-shadow: rgba(0, 0, 0, 0.45) 0 5px 60px;
}

.kl-popup-box--sm {
  width: 340px;
}

.kl-popup__btn {
  min-width: 80px;
  margin-left: 8px;
  margin-top: 8px;

  display: flex;
  justify-content: center;
  align-items: center;
}

.kl-popup__btn img {
  margin-left: 2px;
  margin-right: 5px;
}

.kl-popup__btn__text {
  flex-grow: 1;
  text-align: center;
}

.kl-popup__icon-error,
.kl-popup__icon-ok,
.kl-popup__icon-warning,
.kl-popup__icon-upload {
  background-size: cover;
  background-repeat: no-repeat;
  width: 1.1rem;
  height: 1.1rem;
  float: left;
  margin-right: 5px;
}
.kl-popup__icon-error {
  background-image: url(../img/ui/error.svg);
}
.kl-popup__icon-ok {
  background-image: url(../img/ui/ok.svg);
}
.kl-popup__icon-warning {
  background-image: url(../img/ui/warning.svg);
}
.kl-popup__icon-upload {
  background-image: url(../img/ui/upload-large.svg);
}

.kl-d-modal-root {
  background: rgba(0, 0, 0, 0.45);
}

.kl-d-modal {
  background: #eee;
  box-shadow: rgba(0, 0, 0, 0.25) 0px 5px 60px;
}

@keyframes topOverlayPulse {
  0% {
  }
  10% {
    padding-top: 12px;
    background-color: var(--kl-color);
  }
  50% {
    padding-top: 7px;
    background-color: var(--kl-color);
  }
  100% {
  }
}

.top-overlay {
  position: absolute;
  left: 0;
  top: 0;
  width: calc(100% - 271px);
  z-index: 100;
  animation-duration: 0.4s;
  animation-timing-function: ease-in-out;
  pointer-events: none;
  user-select: none;
  -webkit-user-select: none; // safari

  display: flex;
  justify-content: center;
}
.top-overlay--inner {
  text-align: center;
  padding: 7px 10px;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  /*box-shadow: 0 0 15px 3px rgba(255, 255, 255, 0.3);*/
  /*text-shadow: 0.5px 0.5px rgba(0,0,0,0.5);*/
}

.kl-checkbox {
  display: block;
  user-select: none;
  -webkit-user-select: none; // safari
}
.kl-checkbox--highlight {
  background: #bed5ff;
}

.kl-checkbox__inner {
  cursor: pointer;
  color: rgba(0, 0, 0, 0.6);
}

.kl-checkbox__inner:hover {
  color: rgba(0, 0, 0, 1);
}

.kl-layer {
  width: 270px;
  height: 36px;
  // padding-left: 25px;
  background-color: rgb(220, 220, 220);
  border: 1px solid rgb(158, 158, 158);
  position: absolute;
  left: 0;
  transition: all 0.1s linear;
  // border-radius: 5px;
  box-sizing: border-box;
  background-image: linear-gradient(to top, rgba(188, 188, 188, 0.1) 0%, rgba(255, 255, 255, 0.3) 100%);

  .kl-layer__label,
  .kl-layer__opacity-label {
    color: #666;
  }
}

.kl-layer--selected {
  background: #fff;
  border: 1px solid var(--active-highlight-color);
  z-index: 1;

  .kl-layer__label {
    color: #000;
  }
}

.info-hint {
  background: rgb(255, 132, 115);
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #d00000;
  margin-bottom: 10px;
}

textarea,
input {
  border: 1px solid #b2b2b2;
  box-sizing: border-box;
  background: #fff;
  padding: 5px;
  font-size: 0.95em;
}

textarea:focus,
input:focus {
  border: 1px solid #636363;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  background: #fff;
}

.kl-textarea::placeholder,
.kl-input::placeholder {
  color: #929292;
}

.popup-x {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #888;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none; // safari
  filter: brightness(0.9);
}
.popup-x:hover {
  color: #000;
  filter: brightness(0.5);
}

.brush-alpha,
.brush-alpha--selected {
  width: 35px;
  height: 35px;
  cursor: pointer;
  box-sizing: border-box;
  border: 1px solid #aaa;
  margin-right: 1px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
}

.brush-alpha {
  opacity: 0.4;
  cursor: pointer;
}

.brush-alpha--selected {
  opacity: 1;
  cursor: default;
  background-color: #fff;
  border: 1px solid #76a6ff;
}

iframe {
  border: none;
  box-sizing: border-box;
}

.tabrow {
  background: linear-gradient(to top, rgb(208, 208, 208) 0%, rgba(208, 208, 208, 0) 80%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.7);
  display: flex;
  position: relative;
  z-index: 0;
}

.tabrow__tab {
  user-select: none;
  -webkit-user-select: none; // safari
  cursor: pointer;
  flex-grow: 1;
  height: 100%;
  text-align: center;
  opacity: 0.6;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;

  background-repeat: no-repeat;
  background-position: center;

  z-index: 0;
}
.tabrow__tab-hover {
  background-color: #fff;
}

.tabrow__tab:active:not(.tabrow__tab--opened-accented, .tabrow__tab--opened) {
  box-shadow: inset 0 1px #0000004d;
}

.tabrow__tab--opened,
.tabrow__tab--opened-accented {
  background-color: #f5f5f5;
  opacity: 1;
  cursor: default;
  position: relative;
  z-index: 1;
}

.tabrow__tab--opened-accented {
  box-shadow: inset 0 2px 0 0 var(--active-highlight-color);
}

@keyframes fadeInRounding {
  from {
    transform: scale(0, 1);
  }
  to {
  }
}

/*
multiple inverted radius attempts:
- svg background -> extra image to load (or data-url), a bit bulky
- transparent box with rounded corner throwing a box-shadow -> overlapped the icons
    & visual artifacts if clipped
- radial gradient -> pixelated and not really round looking

clip-path seems the cleanest.
 */
.tabrow__tab__rounding-left {
  pointer-events: none;
  position: absolute;
  left: -10px;
  bottom: 0;
  width: 11px;
  height: 10px;
  background: #f5f5f5;
  clip-path: path('M 11 10 11 0 10 0 A 10 10 0 0 1 0 10');
  filter: none !important;

  animation-name: fadeInRounding;
  animation-duration: 0.4s;
  animation-timing-function: cubic-bezier(0.25, 1, 0.5, 1);
  transform-origin: 10px 10px;
}

.tabrow__tab__rounding-right {
  pointer-events: none;
  position: absolute;
  right: -10px;
  bottom: 0;
  width: 11px;
  height: 10px;
  background: #f5f5f5;
  clip-path: path('M 0 0 0 10 11 10 A 10 10 0 0 1 1 0');
  filter: none !important;

  animation-name: fadeInRounding;
  animation-duration: 0.4s;
  animation-timing-function: cubic-bezier(0.25, 1, 0.5, 1);
  transform-origin: 1px 10px;
}

.kl-option-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-top: -4px;
  margin-left: -4px;
}

.kl-option {
  cursor: pointer;
  padding: 6px 10px;
  margin: 4px 0 0 4px;
  font-size: 16px;
  border-radius: 4px;
  // transition: all 0.1s ease-in-out;

  background: var(--ui-bg-color);
  color: rgba(0, 0, 0, 0.6);
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.3);

  user-select: none;
  -webkit-user-select: none; // safari

  &:not(.kl-option-selected) {
    &:hover {
      background: #eee;
    }
    &:active {
      box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.3);
    }
  }
}

.kl-option--small {
  font-size: 14px;
}

.kl-option--custom-el {
  padding: 0;
}

.kl-option--custom-el:not(.kl-option-selected) > * {
  opacity: 0.6;
}

.kl-option-selected {
  background: #fff;
  color: #000;
  box-shadow: inset 0 0 0 2px var(--active-highlight-color);
  cursor: default;
}

.kl-box-toggle {
  cursor: pointer;
  padding: 6px 10px;
  font-size: 16px;
  border-radius: 4px;
  //transition: all 0.1s ease-in-out;

  color: rgba(0, 0, 0, 0.6);
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.3);

  user-select: none;
  -webkit-user-select: none; // safari

  &:not(.kl-box-toggle--active) {
    &:hover {
      background: #eee;
    }
    &:active {
      box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.3);
    }
  }
}

.kl-box-toggle--active {
  background: #fff;
  color: #000;
  box-shadow: inset 0 0 0 2px var(--active-highlight-color);
  cursor: default;
}

.kl-box-toggle--custom-el {
  padding: 0;
}

.kl-box-toggle--custom-el:not(.kl-box-toggle--active) > * {
  opacity: 0.5;
}

.kl-color-option {
  box-shadow: 0 0 0 1px #aaa;
  cursor: pointer;
  user-select: none;
  text-align: center;
  color: #aaa;
  border-radius: 4px;

  &:not(.kl-color-option--active):active {
    box-shadow: 0 0 0 2px #aaa;
  }
}

.kl-color-option--active {
  box-shadow: 0 0 0 2px var(--active-highlight-color), 0 0 5px 0 var(--active-highlight-color);
}

.tool-dropdown-wrapper {
  background: rgb(241, 241, 241);
  animation-name: dropdownOpen;
  animation-duration: 0.1s;
  animation-timing-function: ease-in-out;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.tool-dropdown-button:hover {
  background: #fff;
}

.tool-dropdown-button:active {
  background-color: #ddd;
  /*box-shadow: 0 2px 5px rgba(0,0,0,0.5), inset 2px 2px 10px rgba(0, 0, 0, 0.2);*/
  box-shadow: inset 2px 2px 10px rgba(0, 0, 0, 0.2);
}

@keyframes dropdownOpen {
  from {
    height: 0;
    opacity: 0;
  }
  to {
  }
}

.image-toggle {
  width: 23px;
  height: 23px;
  cursor: pointer;
  border-radius: 4px;

  &:not(.image-toggle-active) {
    &:hover {
      background: #fff;
    }
    &:active {
      box-shadow: #838383 0 0 0 2px;
    }
  }
}
.image-toggle:not(.image-toggle-active) {
  box-shadow: #838383 0 0 0 1px;
  opacity: 0.6;
}
.image-toggle-active {
  box-shadow: var(--active-highlight-color) 0 0 0 2px;
  background-color: #fff;
}
.image-toggle__im {
  width: 100%;
  height: 100%;
  background-size: 83%;
  background-repeat: no-repeat;
  background-position: center;
}

.image-radio-wrapper {
  display: flex;
  gap: 7px;
}

.image-radio-wrapper .image-toggle-active {
  cursor: default;
}

.kl-radio {
  label {
    cursor: pointer;
  }
  label:not(:last-child) {
    margin-right: 10px;
  }
}

.kl-scroller {
  position: fixed;
  cursor: pointer;
  background-color: rgba(155, 155, 155, 0.9);
  filter: invert(1);
  width: 36px;
  height: 36px;
  background-image: url(../img/ui/caret-down.svg);
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
  user-select: none;
}

.kl-scroller:active {
  background-color: rgba(180, 180, 180, 0.9);
}

.kl-table {
  border-spacing: 0;
  td {
    padding: 0;
  }
}

.kl-2-tabs {
  display: flex;
  justify-content: center;

  .kl-2-tabs__left,
  .kl-2-tabs__right {
    width: 150px;
    padding: 10px 5px;
    text-align: center;
    transition: background 0.1s ease-in-out;

    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.6);
    color: rgba(0, 0, 0, 0.6);
    border-bottom: none;

    &:hover {
      background: #eee;
    }
  }

  .kl-2-tabs__left {
    border-top-left-radius: 10px;
    border-right: none;
  }

  .kl-2-tabs__right {
    border-top-right-radius: 10px;
    border-left: none;
  }

  .kl-2-tabs--active {
    color: #000;
    background: #fff;
    border: 1px solid var(--active-highlight-color);
    border-bottom: none;
    pointer-events: none;
    &:hover {
      background: #fff;
    }
  }
}

.kl-layer-preview {
  color: #555;
  canvas {
    box-shadow: 0 0 0 1px #9e9e9e;
  }
}

.kl-edit-crop-preview {
  border-top: 1px solid #bbb;
  border-bottom: 1px solid #bbb;
}

.kl-edit-crop-preview__sel {
  position: absolute;
  box-shadow: 0 0 0 1px #fff, 0 0 0 2px #000, 0 0 40px 1px #000;
  pointer-events: none;
}

.kl-text-error {
  color: #f00;
}

.kl-file-no-autosave {
  text-align: center;
  margin-top: 10px;
  background: rgb(243, 243, 161);
  padding: 5px 5px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 15px;
}

.kl-preview-wrapper {
  margin-left: -20px;
  background-color: #9e9e9e;
  margin-top: 10px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 1px inset, rgba(0, 0, 0, 0.2) 0px -1px inset;
  overflow: hidden;
  position: relative;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kl-preview-wrapper__canvas {
  position: relative;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.kl-transparent-preview {
  box-shadow: rgba(0, 0, 0, 0.2) 0px 1px inset, rgba(0, 0, 0, 0.2) 0px -1px inset;
}

.kl-transparent-preview__canvas {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.kl-text-preview-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.kl-merge-preview {
  display: block;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}

.kl-stabilizer {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.6);
  margin-right: -1px;
}

.kl-toolspace-note {
  color: #534a3c;
  background: rgba(255, 255, 0, 0.5);
  border: 1px solid #e7d321;
  border-radius: 5px;
  padding: 8px;
  text-align: center;
}

.kl-import-note {
  background: rgba(255, 255, 0, 0.5);
  padding: 8px;
  margin-top: 5px;
  margin-bottom: 5px;
  border: 1px solid #e7d321;
  border-radius: 5px;
}

.kl-curves-graph {
  canvas {
    background: #c6c6c6;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.3);
  }
}

.kl-curves-graph__grip {
  position: absolute;
  background: #fff;
  cursor: move;
  box-shadow: inset 0 0 0 2px #000;
  user-select: none;
  touch-action: none;
}

.kl-storage-preview {
  grid-area: preview;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  position: relative;
  box-shadow: inset 0 0 0 1px #aaa;
  background: #cdcdcd;
  color: #545454;
  min-height: 67px;
}

.kl-storage-preview__im {
  display: block;
  max-width: calc(100% - 2px);
  max-height: calc(100% - 2px);
  margin: 0 auto;
  box-shadow: 0 0 0 1px #aaa;
  pointer-events: none;
}

.kl-info-btn {
  cursor: pointer;
  width: 19px;
  height: 19px;
  border-radius: 100%;
  text-align: center;
  line-height: 19px;
  font-weight: bold;
  box-shadow: inset 0 0 0 1px #000;
  user-select: none;
}

.kl-overlay-toolspace {
  position: absolute;
  left: 500px;
  top: 500px;
  background: rgb(221, 221, 221);
  display: none;
  border: 1px solid #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.kl-file-save-row {
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  margin: 10px 10px 0 10px;
  padding-bottom: 10px;
  padding-right: 10px;
  display: flex;
}

.kl-dropdown-menu {
  position: absolute;
  margin-top: 1px;
  overflow: hidden;

  background: #ddd;
  box-shadow: 0 2px 5px #00000080;
  border-radius: 4px;
}

.kl-pinch-overlay {
  background: rgba(255, 255, 255, 0.5);
}
