import { aiStyles } from '@/api/newBoard'
import { useNavigate } from 'react-router-dom'
import { useState, useEffect } from 'react'

type StyleItem = {
  id: number
  title?: string
  color: string
}

export default function Index() {
  const navigate = useNavigate()
  const [styleList, setStyleList] = useState<any[]>([])

  const settings: StyleItem[] = [
    {
      id: 20,
      color: '#FBF1D5'
    },
    {
      id: 21,
      color: '#FFFFFF'
    },
    {
      id: 22,
      color: '#B76C27'
    },
    {
      id: 23,
      color: '#D0E3F7'
    }
  ]

  useEffect(() => {
    getAIStyles()
  }, [])

  const getAIStyles = async () => {
    const { data } = await aiStyles()
    const newData = data.map((item: StyleItem) => {
      const value = settings.find((i) => item.id === i.id)
      return { ...item, ...value }
    })
    console.log('🚀 ~ getAIStyles ~ data:', newData)
    setStyleList(newData)
  }

  const toBoard = (item: any) => {
    navigate(`/newBoard`)
    localStorage.themeStyle = JSON.stringify(item)
  }

  return (
    <div id="content" className="w-screen h-screen select-none overflow-hidden touch-pan-x p-[60px] setting-bg ">
      <div className="flex w-full h-[60px] justify-between items-center ">
        <span className="text-[45px]"> AI 画影随行</span>
        <div className="sbml-ai w-[243px] h-[53px]"></div>
      </div>
      <div className="border-t-[2px]  boder-[#212121] my-[45px]"> </div>
      <div className="flex w-full h-full justify-between flex-wrap">
        {styleList?.map((item) => {
          const { id: styleId } = JSON.parse(localStorage.themeStyle ?? '{}')
          return (
            <div
              key={'aiCard' + item.id}
              className="flex flex-col w-[422px] h-[491px] p-[20px] border-[2px] border-[#d1d1d1] bg-white rounded-[23px] overflow-hidden cursor-pointer hover:bg-[#f5f5f5] hover:shadow-lg mb-4"
              onClick={() => toBoard(item)}
            >
              <div className="relative w-full h-auto rounded-[23px]  mb-[22px] overflow-hidden">
                <div className={` ${styleId === item.id ? 'block' : 'hidden'} currentStyle absolute left-0 top-0 w-full h-[100px]`}>
                  <div className=" absolute top-[15px] right-[15px] w-[115px] h-[38px] text-center tex rounded-[8px] text-white text-[23px]/[38px]">当前展示</div>
                </div>

                <img src={item.icon} alt="" className="w-full h-full" />
              </div>
              <p className="text-center">{item.title}</p>
            </div>
          )
        })}
      </div>
    </div>
  )
}
