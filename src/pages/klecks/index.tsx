import Klecks from '@/utils/klecks/embed'
import { useEffect, useRef } from 'react'
import '@/utils/klecks/app/style/embed.scss'

export default function Index() {
  const klecksRef = useRef<Klecks>()
  useEffect(() => {
    /*
    Using Klecks in a drawing community:
    - on first time opening, start with a manually created project (klecks.openProject)
    - on submit, upload psd (and png) to the server
    - on continuing a drawing, read psd that was stored on server (klecks.readPsd -> klecks.openProject)
     */

    const psdURL = ''

    let saveData = (function () {
      // let a = document.createElement('a')
      // document.body.appendChild(a)
      // a.style = 'display: none'
      // return function (blob, fileName) {
      //   let url = window.URL.createObjectURL(blob)
      //   a.href = url
      //   a.download = fileName
      //   a.click()
      //   window.URL.revokeObjectURL(url)
      // }
    })()

    const klecks = new Klecks({
      // disableAutoFit: true,
      embedUrl: '',
      onSubmit: (onSuccess, onError) => {
        // download png
        // saveData(klecks.getPNG(), 'drawing.png')

        /*// download psd
            klecks.getPSD().then((blob) => {
                saveData(blob, 'drawing.psd');
            });*/

        setTimeout(() => {
          onSuccess()
          location.reload()
        }, 500)
      }
    })
    setTimeout(() => {
      klecks.instance?.klApp?.layersUi.addBtn.click()
      klecks.instance?.klApp?.layersUi.addBtn.click()
      // console.log(klecks.instance?.klApp?.layersUi.layerElArr)
      console.log(klecks.instance?.klApp?.layersUi.layerElArr)

      // klecks.instance?.klApp?.layersUi.onSelect(1)
    }, 1000)
    klecksRef.current = klecks
    const root = document.getElementById('klecks-root')
    const h = root?.clientHeight || 500
    const w = (root?.clientWidth || 500)
    console.log(w, h)
    klecks.openProject({
      width: w,
      height: h,
      layers: [
        {
          name: 'Background',
          isVisible: true,
          opacity: 1,
          mixModeStr: 'source-over',
          image: (() => {
            const canvas = document.createElement('canvas') as HTMLCanvasElement
            canvas.width = w
            canvas.height = h
            const ctx = canvas.getContext('2d') as CanvasRenderingContext2D
            ctx.save()
            ctx.fillStyle = '#dddddd'
            ctx.fillRect(0, 0, canvas.width, canvas.height)
            ctx.restore()
            return canvas
          })()
        }
      ]
    })
  }, [])

  return (
    <div className="h-screen w-screen flex_center flex-col">
      <div className="h-[50px] w-full bg-slate-400">title</div>
      <p>
        <button
          onClick={() => {
            klecksRef.current?.instance?.klApp?.klCanvas.layerFill(0, {
              r: 255,
              g: 0,
              b: 0
            })
          }}
        >
          AAA
        </button>
        <button
          onClick={() => {
            klecksRef.current?.instance?.klApp?.klColorSlider.setColor2({
              r: 255,
              g: 255,
              b: 0
            })
          }}
        >
          setColor
        </button>
        <button
          onClick={() => {
            klecksRef.current?.instance?.klApp?.layersUi.activateLayer(0)
            klecksRef.current?.instance?.klApp?.layersUi.onSelect(0)
          }}
        >
          BBB
        </button>
      </p>
      <div className="relative w-full flex-1">
        <div id="loading-screen" className="text-[#eee] text-center w-screen text-[25px] mt-[100px]">
          Loading Klecks
        </div>
        <div id="klecks-root" className="absolute left-[400px] w-[400px] h-[400px] z-10"></div>
      </div>
    </div>
  )
}
