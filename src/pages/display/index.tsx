import { useNavigate, useSearchParams, useParams } from 'react-router-dom'
import { useState, useEffect, useCallback } from 'react'
import { getQrcode } from '@/api/newBoard'
import useGetState from '@/hooks/useGetState'
import { BodyType, ipcOn, ipcRemoveListener } from '@/utils/electron'
import { TbLoader } from 'react-icons/tb'

export default function Index() {
  const { id: paramId } = useParams()
  const [generateImg, setGenerateImg] = useState<string>('') // 生成图片
  const [qrcode, setQrCode] = useState<string>('') // 二维码
  const [loading, setLoading] = useState<boolean>(false) //
  const { id: styleId, title: styleName } = JSON.parse(localStorage.themeStyle ?? '{}')

  useEffect(() => {
    ipcOn('msg', onMsg)

    ipcOn('closed', onClosed)
    return () => {
      ipcRemoveListener('msg', onMsg)
      ipcRemoveListener('closed', onClosed)
    }
  }, [])

  const onMsg = useCallback(async (event: any, message: BodyType, ...args: any[]) => {
    const { data } = message
    if ('loading' in data) {
      setLoading(true)
    }
    if ('loaded' in data) {
      setGenerateImg(data.loaded)
      setLoading(false)
    }
  }, [])

  const onClosed = (event: any, message: any) => {
    if (message.data?.winName === 'mainWin') {
      window.close()
    }
  }

  useEffect(() => {
    if (generateImg) {
      getQrcodeRequst()
    }
  }, [generateImg])

  const getQrcodeRequst = async () => {
    const res = await getQrcode({
      imageUrl: generateImg,
      styleName
    })
    if (res.code !== 200) {
      return
    }
    setQrCode(res.data)
    // return res.data
  }

  const shareUrl = window.location.origin + '/shareCover?url=' + generateImg
  // console.log('🚀 ~ Index ~ shareUrl:', shareUrl)

  return (
    <div id="content" className={`play-theme-${styleId} w-screen h-screen  select-none  overflow-hidden touch-pan-x`}>
      {/* <div className="absolute top-0 left-0 z-[11] w-full h-[40px] drag"></div> */}
      <div className="frame">
        <div className="board w-[975px] h-[731px] bg-white overflow-hidden">
          {generateImg ? <img className="w-full h-full" src={generateImg} alt="" /> : ''}

          <div className={` ${loading ? 'flex' : 'hidden'} absolute left-0 top-0 z-10 w-full h-full justify-center items-center bg-[rgba(117,116,116,0.2)]`}>
            <TbLoader className="animate-spin" />
          </div>
        </div>
      </div>

      <div className="absolute bottom-[186px] right-[64px] w-[300px] h-[386px] p-[45px] bg-[#F5F5F5] rounded-[23px] flex_center flex-col shadow-md">
        <div className=" mb-[20px]">神笔马良 AI</div>
        <div className="w-[188px] h-[188px] rounded-[10px] p-[8px] overflow-hidden bg-white mb-[10px]">
          {/* <QRCodeSVG value={shareUrl} style={{ width: '100%', height: '100%' }} /> */}
          {generateImg && qrcode ? <img src={qrcode} className="w-full h-full" alt="" /> : ''}
        </div>
        <p className="text-[26px] text-[#5F5F5F]">扫码，免费领图</p>
      </div>
    </div>
  )
}
